<script setup lang="ts">
  import type {
    AccountSubjectResponseData,
    AIAccountSubjectItem,
    AIAccountSubjectResponseData,
    SubjectMappingEntry,
    SubjectMappingUpdateParams,
    SyncAccountSubjectsParams,
  } from '../../../api/jsj-ai/types';

  import { computed, onMounted, ref, watch } from 'vue';

  import { useUserStore } from '@vben/stores';

  import { ReloadOutlined, SyncOutlined } from '@ant-design/icons-vue';
  import {
    Button,
    Card,
    message,
    Modal,
    Space,
    Spin,
    Table,
    Tabs,
    Tree,
  } from 'ant-design-vue';

  import {
    fetchAccountSubjectList,
    fetchAIAccountSubjectList,
    syncAccountSubjects,
    updateSubjectMapping,
  } from '../../../api/jsj-ai/api-v2';
  import { useCompanySelection } from '../../../hooks/jsj-ai/ai-chat/useCompanySelection';

  // 全局状态
  const { selectedCompany } = useCompanySelection();
  const userStore = useUserStore();

  // 响应式数据
  const loading = ref(false);
  const syncLoading = ref(false);
  const subjectData = ref<AIAccountSubjectResponseData | null>(null);
  const activeTabKey = ref('asset');

  // 实际科目数据相关
  const actualSubjectData = ref<AccountSubjectResponseData | null>(null);
  const subjectModalVisible = ref(false);
  const subjectModalLoading = ref(false);
  const currentMappingRecord = ref<any>(null);
  const selectedActualSubject = ref<string>('');

  // 科目详情弹窗相关
  const subjectDetailModalVisible = ref(false);
  const currentSubjectDetail = ref<any>(null);
  const subjectDetailData = ref<any[]>([]);

  // 辅助核算数据相关 - 已移除，改为直接映射单字母代码

  // 科目类别配置
  const subjectCategories = [
    { key: 'asset', name: '资产' },
    { key: 'liability', name: '负债' },
    { key: 'equity', name: '所有者权益' },
    { key: 'cost', name: '成本' },
    { key: 'profit', name: '损益' },
  ];

  // 表格列定义
  const tableColumns = [
    {
      dataIndex: 'name',
      key: 'name',
      title: 'AI记账科目',
      width: '200px',
    },
    {
      dataIndex: 'mappedSubject',
      key: 'mappedSubject',
      title: '实际适用记账科目',
      width: '200px',
    },
    {
      dataIndex: 'auxiliaryTypes',
      key: 'auxiliaryTypes',
      title: '辅助核算',
      width: '100px',
    },
  ];

  // 分页配置
  const pagination = ref({
    current: 1,
    pageSize: 20,
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条`,
    total: 0,
  });

  // 只取第一层科目数据
  const flattenSubjects = (
    subjects: AIAccountSubjectItem[],
  ): AIAccountSubjectItem[] => {
    // 直接返回第一层数据，不处理children
    return subjects || [];
  };

  // 按类别分组的表格数据源
  const getTableDataSourceByCategory = (category: string) => {
    if (
      !subjectData.value ||
      !subjectData.value.subjects[
        category as keyof typeof subjectData.value.subjects
      ]
    )
      return [];

    const categorySubjects = subjectData.value.subjects[
      category as keyof typeof subjectData.value.subjects
    ] as AIAccountSubjectItem[];
    if (!Array.isArray(categorySubjects)) return [];

    const flatSubjects = flattenSubjects(categorySubjects);

    // 转换为表格数据格式
    return flatSubjects.map((item, index) => ({
      auxiliaryTypes: formatAssistantTypes(item.assistantType),
      code: item.code,
      key: `${category}_${item.code || index}`,
      mappedSubject: item.cfgFullName || '--',
      mapping_type: item.mapping_type,
      name: item.fullName || item.name,
      original: item,
    }));
  };

  // 格式化辅助核算类型显示
  const formatAssistantTypes = (assistantType?: string | string[]): string => {
    if (!assistantType) return '--';

    // 如果是字符串，按逗号分割
    let types: string[] = [];
    if (typeof assistantType === 'string') {
      types = assistantType
        .split(',')
        .map((t) => t.trim())
        .filter(Boolean);
    } else if (Array.isArray(assistantType)) {
      types = assistantType;
    }

    if (types.length === 0) return '--';

    const typeNames: string[] = [];
    types.forEach((type) => {
      let typeName = type;

      // 根据单字母代码或英文单词获取对应的中文名称
      switch (type.toLowerCase()) {
        case 'c':
        case 'customer': {
          typeName = '客户';
          break;
        }
        case 'd':
        case 'department': {
          typeName = '部门';
          break;
        }
        case 'e':
        case 'employee': {
          typeName = '员工';
          break;
        }
        case 'i':
        case 'inventory': {
          typeName = '存货';
          break;
        }
        case 'p':
        case 'project': {
          typeName = '项目';
          break;
        }
        case 's':
        case 'supplier': {
          typeName = '供应商';
          break;
        }
        default: {
          // 如果不匹配，保持原类型名
          typeName = type;
          break;
        }
      }

      if (typeName) {
        typeNames.push(typeName);
      }
    });

    return typeNames.join(', ') || '--';
  };

  // 获取科目类别名称
  const getCategoryName = (category: string): string => {
    const categoryNames: Record<string, string> = {
      asset: '资产类',
      cost: '成本类',
      equity: '权益类',
      liability: '负债类',
      profit: '损益类',
    };
    return categoryNames[category] || category;
  };

  // 获取实际科目列表
  const fetchActualSubjectList = async () => {
    if (!selectedCompany.value) {
      message.warning('请先选择公司');
      return;
    }

    try {
      subjectModalLoading.value = true;
      const response = await fetchAccountSubjectList({
        company_name: selectedCompany.value,
      });

      actualSubjectData.value = response;
      console.log('实际科目列表加载成功:', response);
    } catch (error) {
      console.error('获取实际科目列表失败:', error);
      message.error('获取实际科目列表失败');
    } finally {
      subjectModalLoading.value = false;
    }
  };

  // 打开科目选择弹窗
  const openSubjectModal = async (record: any) => {
    currentMappingRecord.value = record;
    selectedActualSubject.value = '';
    subjectModalVisible.value = true;

    // 如果还没有加载实际科目数据，则加载
    if (!actualSubjectData.value) {
      await fetchActualSubjectList();
    }
  };

  // 关闭科目选择弹窗
  const closeSubjectModal = () => {
    subjectModalVisible.value = false;
    currentMappingRecord.value = null;
    selectedActualSubject.value = '';
  };

  // 确认选择科目
  const confirmSubjectSelection = async () => {
    if (!selectedActualSubject.value || !currentMappingRecord.value) {
      message.warning('请先选择科目');
      return;
    }

    if (!subjectData.value?.company_id) {
      message.error('公司信息不完整，请刷新页面重试');
      return;
    }

    try {
      subjectModalLoading.value = true;

      const entries: SubjectMappingEntry[] = [
        {
          ai: currentMappingRecord.value.name,
          cfg: selectedActualSubject.value,
        },
      ];

      const params: SubjectMappingUpdateParams = {
        company_id: subjectData.value.company_id,
        entries,
      };

      await updateSubjectMapping(params);

      message.success('科目映射保存成功');
      closeSubjectModal();

      // 重新获取数据以更新显示
      await fetchSubjectList();
    } catch (error) {
      console.error('保存科目映射失败:', error);
      message.error('保存科目映射失败');
    } finally {
      subjectModalLoading.value = false;
    }
  };

  // 构建实际科目树形数据
  const buildActualSubjectTreeData = (
    subjects: any[],
    level: number = 0,
  ): any[] => {
    return subjects.map((item) => ({
      isLeaf: !item.children || item.children.length === 0,
      key: item.fullName || item.name,
      original: item,
      // 除了五大类（第0层的祖父节点）外，其他节点都可以选中
      selectable: level > 0,
      title: `${item.code || ''} ${item.fullName || item.name}`,
      children:
        item.children && item.children.length > 0
          ? buildActualSubjectTreeData(item.children, level + 1)
          : undefined,
    }));
  };

  // 实际科目树形数据
  const actualSubjectTreeData = computed(() => {
    if (!actualSubjectData.value) return [];

    const allTreeData: any[] = [];

    Object.entries(actualSubjectData.value.subjects).forEach(
      ([category, categorySubjects]) => {
        if (Array.isArray(categorySubjects) && categorySubjects.length > 0) {
          const categoryNode = {
            key: `actual_${category}`,
            selectable: false, // 五大类（祖父节点）不可选择
            title: getCategoryName(category),
            children: buildActualSubjectTreeData(categorySubjects, 1), // 从第1层开始，五大类是第0层
          };
          allTreeData.push(categoryNode);
        }
      },
    );

    return allTreeData;
  });

  // 处理实际科目树选择
  const handleActualSubjectSelect = (
    selectedKeysParam: (number | string)[],
    info: any,
  ) => {
    if (selectedKeysParam.length > 0 && info.selected) {
      const selectedNode = info.node;
      if (selectedNode.selectable) {
        selectedActualSubject.value = selectedNode.key;
        console.log('选择的实际科目:', selectedNode.title);
      } else {
        message.warning('请选择具体的记账科目，不能选择科目大类');
      }
    } else {
      selectedActualSubject.value = '';
    }
  };

  // 打开科目详情弹窗
  const openSubjectDetailModal = (record: any) => {
    currentSubjectDetail.value = record;
    subjectDetailModalVisible.value = true;

    // 查找对应的科目及其子科目
    findSubjectWithChildren(record.mappedSubject);
  };

  // 关闭科目详情弹窗
  const closeSubjectDetailModal = () => {
    subjectDetailModalVisible.value = false;
    currentSubjectDetail.value = null;
    subjectDetailData.value = [];
  };

  // 查找科目及其所有子科目
  const findSubjectWithChildren = (subjectName: string) => {
    if (!actualSubjectData.value) return;

    const result: any[] = [];

    // 遍历所有科目类别
    Object.entries(actualSubjectData.value.subjects).forEach(
      ([_category, categorySubjects]) => {
        if (Array.isArray(categorySubjects)) {
          const found = findSubjectInCategory(categorySubjects, subjectName);
          if (found) {
            result.push(...found);
          }
        }
      },
    );

    subjectDetailData.value = result;
  };

  // 在科目类别中查找指定科目
  const findSubjectInCategory = (
    subjects: any[],
    targetName: string,
  ): any[] | null => {
    for (const subject of subjects) {
      const fullName = subject.fullName || subject.name;

      // 如果找到匹配的科目，返回该科目及其所有子科目
      if (fullName === targetName) {
        return flattenAllSubjects([subject]);
      }

      // 递归查找子科目
      if (subject.children && subject.children.length > 0) {
        const found = findSubjectInCategory(subject.children, targetName);
        if (found) return found;
      }
    }
    return null;
  };

  // 扁平化显示所有科目（包括子科目）
  const flattenAllSubjects = (subjects: any[]): any[] => {
    const result: any[] = [];

    const flatten = (items: any[], level = 0) => {
      items.forEach((item) => {
        result.push({
          ...item,
          displayName: `${item.code || ''} ${item.fullName || item.name}`,
          indentedName: `${'　'.repeat(level)}${item.code || ''} ${item.fullName || item.name}`,
          level,
        });

        if (item.children && item.children.length > 0) {
          flatten(item.children, level + 1);
        }
      });
    };

    flatten(subjects);
    return result;
  };
  const fetchSubjectList = async () => {
    if (!selectedCompany.value) {
      message.warning('请先选择公司');
      return;
    }

    try {
      loading.value = true;
      const subjectResponse = await fetchAIAccountSubjectList({
        company_name: selectedCompany.value,
      });

      subjectData.value = subjectResponse;
      pagination.value.total = subjectResponse.total_count || 0;

      console.log('科目列表加载成功:', subjectResponse);
    } catch (error) {
      console.error('获取科目列表失败:', error);
      message.error('获取科目列表失败');
    } finally {
      loading.value = false;
    }
  };

  // 同步科目辅助核算
  const handleSyncSubjects = async () => {
    if (!selectedCompany.value || !subjectData.value?.company_id) {
      message.warning('请先选择公司并加载科目数据');
      return;
    }

    const username = userStore.userInfo?.username;
    if (!username) {
      message.error('用户信息不完整，请重新登录');
      return;
    }

    try {
      syncLoading.value = true;

      const params: SyncAccountSubjectsParams = {
        company_id: subjectData.value.company_id,
        username,
      };

      await syncAccountSubjects(params);

      message.success('科目同步成功');
      // 重新获取科目列表
      await fetchSubjectList();
    } catch (error) {
      console.error('同步科目失败:', error);
      message.error('同步科目失败');
    } finally {
      syncLoading.value = false;
    }
  };

  // 监听公司变化
  watch(selectedCompany, () => {
    subjectData.value = null;
    actualSubjectData.value = null; // 清空实际科目数据
    pagination.value.current = 1;
    if (selectedCompany.value) {
      fetchSubjectList();
    }
  });

  // 组件挂载时的初始化
  onMounted(() => {
    if (selectedCompany.value) {
      fetchSubjectList();
    }
  });
</script>

<template>
  <div class="subject-mapping-container">
    <!-- 页面头部 -->
    <Card class="header-card">
      <div class="header-content">
        <div class="company-info">
          <span class="company-label">当前公司：</span>
          <span class="company-name">{{ selectedCompany || '未选择' }}</span>
        </div>

        <Space>
          <Button
            type="primary"
            :loading="syncLoading"
            @click="handleSyncSubjects"
          >
            <template #icon>
              <SyncOutlined />
            </template>
            同步会计科目
          </Button>

          <Button :loading="loading" @click="fetchSubjectList">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新列表
          </Button>
        </Space>
      </div>
    </Card>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- AI科目列表 -->
      <Card class="main-panel">
        <Tabs v-model:active-key="activeTabKey" class="subject-tabs">
          <Tabs.TabPane
            v-for="category in subjectCategories"
            :key="category.key"
            :tab="category.name"
          >
            <Table
              :columns="tableColumns"
              :data-source="getTableDataSourceByCategory(category.key)"
              :pagination="{
                current: 1,
                pageSize: 20,
                showQuickJumper: true,
                showSizeChanger: true,
                showTotal: (total: number) => `共 ${total} 条`,
                total: getTableDataSourceByCategory(category.key).length,
              }"
              :loading="loading"
              size="small"
              :scroll="{ y: 'calc(100vh - 460px)' }"
              row-key="key"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'mappedSubject'">
                  <div class="mapped-subject">
                    <span
                      v-if="record.mappedSubject !== '--'"
                      class="mapped-subject-text clickable mapped-text"
                      @click="openSubjectModal(record)"
                    >
                      {{ record.mappedSubject }}
                    </span>
                    <div class="subject-actions">
                      <Button
                        v-if="record.mapping_type === 0"
                        type="link"
                        size="small"
                        @click="openSubjectModal(record)"
                      >
                        {{ record.mappedSubject === '--' ? '映射' : '修改' }}
                      </Button>
                      <Button
                        v-if="record.mappedSubject !== '--'"
                        type="link"
                        size="small"
                        @click="openSubjectDetailModal(record)"
                      >
                        查看其他适用科目
                      </Button>
                    </div>
                  </div>
                </template>
              </template>
            </Table>
          </Tabs.TabPane>
        </Tabs>
      </Card>
    </div>

    <!-- 科目选择弹窗 -->
    <Modal
      v-model:open="subjectModalVisible"
      title="选择实际记账科目"
      width="800px"
      :confirm-loading="subjectModalLoading"
      @ok="confirmSubjectSelection"
      @cancel="closeSubjectModal"
    >
      <template #footer>
        <Button @click="closeSubjectModal">取消</Button>
        <Button
          type="primary"
          :disabled="!selectedActualSubject"
          :loading="subjectModalLoading"
          @click="confirmSubjectSelection"
        >
          确认选择
        </Button>
      </template>

      <div class="subject-modal-content">
        <div v-if="currentMappingRecord" class="subject-info">
          <p>
            <strong>AI记账科目：</strong>
            {{ currentMappingRecord.name }}
          </p>
          <p>
            <strong>当前映射：</strong>
            {{ currentMappingRecord.mappedSubject }}
          </p>
        </div>

        <div class="subject-tree-section">
          <h4>请选择实际记账科目：</h4>
          <div class="subject-tree-wrapper">
            <Spin :spinning="subjectModalLoading">
              <Tree
                v-if="actualSubjectTreeData.length > 0"
                :tree-data="actualSubjectTreeData"
                :selected-keys="
                  selectedActualSubject ? [selectedActualSubject] : []
                "
                show-line
                @select="handleActualSubjectSelect"
              />
              <div v-else class="empty-tree">
                <p>
                  {{ subjectModalLoading ? '正在加载...' : '暂无科目数据' }}
                </p>
              </div>
            </Spin>
          </div>
        </div>
      </div>
    </Modal>

    <!-- 科目详情弹窗 -->
    <Modal
      v-model:open="subjectDetailModalVisible"
      title="科目详情"
      width="800px"
      :footer="null"
      @cancel="closeSubjectDetailModal"
    >
      <div class="subject-detail-modal-content">
        <div v-if="currentSubjectDetail" class="subject-info">
          <p>
            <strong>AI记账科目：</strong>
            {{ currentSubjectDetail.name }}
          </p>
          <p>
            <strong>当前映射科目：</strong>
            {{ currentSubjectDetail.mappedSubject }}
          </p>
        </div>

        <div class="subject-detail-section">
          <div class="subject-detail-description">
            AI在对"{{
              currentSubjectDetail?.name
            }}"科目进行核算时，会根据实际情况在以下科目表内进行选择，请确认范围是否正确：
          </div>
          <div class="subject-detail-table">
            <!-- 表格头部 -->
            <div class="table-header">
              <div class="header-cell code-cell">科目代码</div>
              <div class="header-cell name-cell">科目名称</div>
              <div class="header-cell auxiliary-cell">辅助核算</div>
            </div>
            <!-- 表格内容 -->
            <div class="table-body">
              <div
                v-for="(item, index) in subjectDetailData"
                :key="index"
                class="table-row"
                :class="{ 'is-parent': item.level === 0 }"
              >
                <div class="code-cell table-cell">{{ item.code || '--' }}</div>
                <div class="name-cell table-cell">{{ item.indentedName }}</div>
                <div class="auxiliary-cell table-cell">
                  {{ formatAssistantTypes(item.assistantType) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
  .subject-mapping-container {
    box-sizing: border-box;
    min-width: 1200px;
    height: calc(100vh - 88px);
    padding: 8px 12px;
    overflow-y: auto;
  }

  .header-card {
    margin-bottom: 8px;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
  }

  .company-info {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .company-label {
    font-size: 14px;
    font-weight: 500;
    color: #666;
  }

  .company-name {
    padding: 4px 12px;
    font-size: 14px;
    font-weight: 600;
    color: #1890ff;
    background: #f0f8ff;
    border-radius: 4px;
  }

  .main-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
  }

  .main-panel {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
  }

  :deep(.ant-card-body) {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0;
    overflow: hidden;
  }

  .mapped-subject {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
  }

  .subject-actions {
    display: flex;
    flex-shrink: 0;
    gap: 6px;
  }

  .mapped-subject-text {
    flex: 1;
    margin-right: 8px;
    font-size: 14px;

    &.clickable {
      padding: 2px 6px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        color: #1890ff;
        background-color: #f0f8ff;
      }
    }
  }

  .mapped-text {
    font-weight: 500;
    color: #52c41a;
  }

  .unmapped-text {
    font-style: italic;
    color: #ff4d4f;
  }

  .empty-tree {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #8c8c8c;
  }

  /* 标签页样式 - 参考 voucher/original */
  .subject-tabs {
    margin-bottom: 0;
    font-size: 14px;
  }

  .subject-tabs :deep(.ant-tabs-nav) {
    padding: 4px 16px 0;
    margin-bottom: 8px !important;
    background: #fff;
  }

  .subject-tabs :deep(.ant-tabs-tab) {
    padding: 8px 16px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
  }

  .subject-tabs :deep(.ant-tabs-tab:hover) {
    color: #1890ff;
  }

  .subject-tabs :deep(.ant-tabs-tab.ant-tabs-tab-active) {
    font-weight: 600;
    color: #1890ff !important;
  }

  .subject-tabs :deep(.ant-tabs-ink-bar) {
    height: 2px;
    background: #1890ff;
    border-radius: 1px;
  }

  .subject-tabs :deep(.ant-tabs-content-holder) {
    padding: 0 16px;
    background: #fff;
  }

  .subject-tabs :deep(.ant-tabs-tabpane) {
    padding: 0;
  }

  /* 表格样式 - 参考 voucher/original */
  :deep(.ant-table) {
    overflow: hidden;
    font-size: 12px;
    border-radius: 0;
  }

  :deep(.ant-table-thead > tr > th) {
    box-sizing: border-box;
    height: 32px;
    padding: 6px 8px !important;
    font-size: 12px;
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
    border-bottom: 2px solid #e8eaed;
  }

  :deep(.ant-table-tbody > tr > td) {
    box-sizing: border-box;
    height: 30px;
    min-height: 30px;
    max-height: 30px;
    padding: 4px 8px;
    font-size: 12px;
    line-height: 22px;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f8f9ff;
  }

  :deep(.ant-table-tbody > tr) {
    height: 30px;
  }

  /* 按钮样式优化 - 参考 voucher/original */
  :deep(.ant-btn) {
    height: 26px;
    padding: 0 10px;
    font-size: 12px;
    border-radius: 4px;
  }

  :deep(.ant-btn-link) {
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 4px;
  }

  :deep(.ant-btn-link:hover) {
    color: #1890ff;
    background-color: #f0f8ff;
  }

  /* 树组件样式优化 */
  :deep(.ant-tree-node-content-wrapper) {
    padding: 8px;
    line-height: 24px;
    border-radius: 4px;
  }

  :deep(.ant-tree-node-content-wrapper:hover) {
    background-color: #f0f8ff;
  }

  :deep(.ant-tree-title) {
    font-size: 14px;
  }

  /* 弹窗样式优化 */
  :deep(.ant-modal) {
    border-radius: 8px;
  }

  :deep(.ant-modal-header) {
    padding: 16px 24px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-modal-title) {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  .subject-modal-content {
    .subject-info {
      padding: 16px;
      margin-bottom: 20px;
      background: #f8f9fa;
      border-left: 4px solid #1890ff;
      border-radius: 6px;

      p {
        margin: 6px 0;
        font-size: 14px;
        line-height: 1.6;

        strong {
          font-weight: 600;
          color: #333;
        }
      }
    }

    .subject-tree-section {
      h4 {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      .subject-tree-wrapper {
        max-height: 400px;
        padding: 12px;
        overflow-y: auto;
        background: #fafafa;
        border: 1px solid #d9d9d9;
        border-radius: 6px;

        .empty-tree {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 120px;
          font-size: 14px;
          color: #8c8c8c;
          background: white;
          border: 1px dashed #d9d9d9;
          border-radius: 4px;
        }
      }
    }
  }

  /* 科目详情弹窗样式 */
  .subject-detail-modal-content {
    .subject-info {
      padding: 12px;
      margin-bottom: 20px;
      background-color: #f8f9fa;
      border-radius: 6px;

      p {
        margin: 4px 0;
        font-size: 14px;

        strong {
          color: #333;
        }
      }
    }

    .subject-detail-section {
      .subject-detail-description {
        padding: 12px;
        margin-bottom: 16px;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background-color: #f0f8ff;
        border-left: 3px solid #1890ff;
        border-radius: 4px;
      }

      .subject-detail-table {
        overflow: hidden;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 6px;

        .table-header {
          display: flex;
          background-color: #fafafa;
          border-bottom: 1px solid #f0f0f0;

          .header-cell {
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 600;
            color: #262626;
            text-align: center;
            border-right: 1px solid #f0f0f0;

            &:last-child {
              border-right: none;
            }

            &.code-cell {
              flex: 0 0 120px;
            }

            &.name-cell {
              flex: 1;
            }

            &.auxiliary-cell {
              flex: 0 0 120px;
            }
          }
        }

        .table-body {
          max-height: 300px;
          overflow-y: auto;

          .table-row {
            display: flex;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background-color: #f5f5f5;
            }

            &.is-parent {
              font-weight: 500;
              background-color: #fafafa;
            }

            .table-cell {
              display: flex;
              align-items: center;
              padding: 8px 12px;
              font-size: 14px;
              color: #333;
              border-right: 1px solid #f0f0f0;

              &:last-child {
                border-right: none;
              }

              &.code-cell {
                flex: 0 0 120px;
                justify-content: center;
                font-family: 'Courier New', monospace;
              }

              &.name-cell {
                flex: 1;
                word-break: break-all;
              }

              &.auxiliary-cell {
                flex: 0 0 120px;
                justify-content: center;
                color: #666;
              }
            }
          }
        }
      }
    }
  }
</style>
