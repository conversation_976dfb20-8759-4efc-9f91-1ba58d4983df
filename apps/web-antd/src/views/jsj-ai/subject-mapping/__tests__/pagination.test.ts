import { describe, it, expect } from 'vitest';
import { ref } from 'vue';

// 模拟分页逻辑
const createPaginationLogic = () => {
  const paginationMap = ref<Record<string, any>>({});

  // 获取指定类别的分页配置
  const getPaginationForCategory = (category: string) => {
    if (!paginationMap.value[category]) {
      paginationMap.value[category] = {
        current: 1,
        pageSize: 20,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total: number) => `共 ${total} 条`,
        total: 0,
      };
    }
    return paginationMap.value[category];
  };

  // 处理分页变化
  const handlePaginationChange =
    (category: string) => (page: number, pageSize: number) => {
      const pagination = getPaginationForCategory(category);
      pagination.current = page;
      pagination.pageSize = pageSize;
    };

  return {
    paginationMap,
    getPaginationForCategory,
    handlePaginationChange,
  };
};

describe('分页功能测试', () => {
  const subjectCategories = [
    { key: 'asset', name: '资产' },
    { key: 'liability', name: '负债' },
    { key: 'equity', name: '所有者权益' },
    { key: 'cost', name: '成本' },
    { key: 'profit', name: '损益' },
  ];

  it('应该为每个科目类别创建独立的分页配置', () => {
    const { getPaginationForCategory } = createPaginationLogic();

    subjectCategories.forEach(category => {
      const pagination = getPaginationForCategory(category.key);
      
      expect(pagination).toBeDefined();
      expect(pagination.current).toBe(1);
      expect(pagination.pageSize).toBe(20);
      expect(pagination.showQuickJumper).toBe(true);
      expect(pagination.showSizeChanger).toBe(true);
      expect(pagination.total).toBe(0);
    });
  });

  it('应该正确处理分页变化', () => {
    const { getPaginationForCategory, handlePaginationChange } = createPaginationLogic();

    const category = 'asset';
    const pagination = getPaginationForCategory(category);
    
    // 初始状态
    expect(pagination.current).toBe(1);
    expect(pagination.pageSize).toBe(20);

    // 模拟分页变化
    const changeHandler = handlePaginationChange(category);
    changeHandler(3, 50);

    // 验证变化
    expect(pagination.current).toBe(3);
    expect(pagination.pageSize).toBe(50);
  });

  it('应该为不同类别维护独立的分页状态', () => {
    const { getPaginationForCategory, handlePaginationChange } = createPaginationLogic();

    // 设置资产类分页
    const assetPagination = getPaginationForCategory('asset');
    const assetChangeHandler = handlePaginationChange('asset');
    assetChangeHandler(2, 30);

    // 设置负债类分页
    const liabilityPagination = getPaginationForCategory('liability');
    const liabilityChangeHandler = handlePaginationChange('liability');
    liabilityChangeHandler(5, 10);

    // 验证独立性
    expect(assetPagination.current).toBe(2);
    expect(assetPagination.pageSize).toBe(30);
    
    expect(liabilityPagination.current).toBe(5);
    expect(liabilityPagination.pageSize).toBe(10);
  });

  it('应该正确格式化总数显示', () => {
    const { getPaginationForCategory } = createPaginationLogic();

    const pagination = getPaginationForCategory('asset');
    const showTotalFunc = pagination.showTotal;

    expect(showTotalFunc(100)).toBe('共 100 条');
    expect(showTotalFunc(0)).toBe('共 0 条');
    expect(showTotalFunc(1)).toBe('共 1 条');
  });

  it('应该支持重置分页状态', () => {
    const { getPaginationForCategory, handlePaginationChange } = createPaginationLogic();

    const category = 'asset';
    const pagination = getPaginationForCategory(category);
    
    // 修改分页状态
    const changeHandler = handlePaginationChange(category);
    changeHandler(5, 50);
    
    expect(pagination.current).toBe(5);
    expect(pagination.pageSize).toBe(50);

    // 重置到第一页
    pagination.current = 1;
    
    expect(pagination.current).toBe(1);
    expect(pagination.pageSize).toBe(50); // pageSize 保持不变
  });
});
