import { describe, it, expect } from 'vitest';

// 模拟构建实际科目树形数据的函数
const buildActualSubjectTreeData = (subjects: any[], level: number = 0): any[] => {
  return subjects.map((item) => ({
    isLeaf: !item.children || item.children.length === 0,
    key: item.fullName || item.name,
    original: item,
    // 除了五大类（第0层的祖父节点）外，其他节点都可以选中
    selectable: level > 0,
    title: `${item.code || ''} ${item.fullName || item.name}`,
    children:
      item.children && item.children.length > 0
        ? buildActualSubjectTreeData(item.children, level + 1)
        : undefined,
  }));
};

// 模拟科目类别名称获取函数
const getCategoryName = (category: string): string => {
  const categoryNames: Record<string, string> = {
    asset: '资产类',
    cost: '成本类',
    equity: '权益类',
    liability: '负债类',
    profit: '损益类',
  };
  return categoryNames[category] || category;
};

// 模拟构建完整的科目树数据
const buildCompleteSubjectTreeData = (actualSubjectData: any) => {
  if (!actualSubjectData) return [];

  const allTreeData: any[] = [];

  Object.entries(actualSubjectData.subjects).forEach(
    ([category, categorySubjects]) => {
      if (Array.isArray(categorySubjects) && categorySubjects.length > 0) {
        const categoryNode = {
          key: `actual_${category}`,
          selectable: false, // 五大类（祖父节点）不可选择
          title: getCategoryName(category),
          children: buildActualSubjectTreeData(categorySubjects, 1), // 从第1层开始，五大类是第0层
        };
        allTreeData.push(categoryNode);
      }
    },
  );

  return allTreeData;
};

describe('科目选择逻辑测试', () => {
  // 模拟科目数据
  const mockSubjectData = {
    subjects: {
      asset: [
        {
          code: '1001',
          name: '库存现金',
          fullName: '库存现金',
          level: 1,
          last: true,
          children: [],
        },
        {
          code: '1002',
          name: '银行存款',
          fullName: '银行存款',
          level: 1,
          last: false,
          children: [
            {
              code: '100201',
              name: '基本存款账户',
              fullName: '银行存款-基本存款账户',
              level: 2,
              last: true,
              children: [],
            },
            {
              code: '100202',
              name: '一般存款账户',
              fullName: '银行存款-一般存款账户',
              level: 2,
              last: true,
              children: [],
            },
          ],
        },
      ],
      liability: [
        {
          code: '2001',
          name: '短期借款',
          fullName: '短期借款',
          level: 1,
          last: true,
          children: [],
        },
      ],
    },
  };

  it('应该正确设置五大类科目为不可选择', () => {
    const treeData = buildCompleteSubjectTreeData(mockSubjectData);
    
    // 检查五大类科目节点
    const assetCategory = treeData.find(node => node.key === 'actual_asset');
    const liabilityCategory = treeData.find(node => node.key === 'actual_liability');
    
    expect(assetCategory?.selectable).toBe(false);
    expect(liabilityCategory?.selectable).toBe(false);
    expect(assetCategory?.title).toBe('资产类');
    expect(liabilityCategory?.title).toBe('负债类');
  });

  it('应该正确设置第一级科目为可选择', () => {
    const treeData = buildCompleteSubjectTreeData(mockSubjectData);
    
    // 获取资产类下的子科目
    const assetCategory = treeData.find(node => node.key === 'actual_asset');
    const firstLevelSubjects = assetCategory?.children;
    
    expect(firstLevelSubjects).toBeDefined();
    expect(firstLevelSubjects?.length).toBe(2);
    
    // 检查第一级科目都可选择
    firstLevelSubjects?.forEach(subject => {
      expect(subject.selectable).toBe(true);
    });
  });

  it('应该正确设置第二级科目为可选择', () => {
    const treeData = buildCompleteSubjectTreeData(mockSubjectData);
    
    // 获取银行存款下的子科目
    const assetCategory = treeData.find(node => node.key === 'actual_asset');
    const bankDeposit = assetCategory?.children?.find((child: any) => child.key === '银行存款');
    const secondLevelSubjects = bankDeposit?.children;
    
    expect(secondLevelSubjects).toBeDefined();
    expect(secondLevelSubjects?.length).toBe(2);
    
    // 检查第二级科目都可选择
    secondLevelSubjects?.forEach(subject => {
      expect(subject.selectable).toBe(true);
    });
  });

  it('应该正确设置科目标题格式', () => {
    const treeData = buildCompleteSubjectTreeData(mockSubjectData);
    
    const assetCategory = treeData.find(node => node.key === 'actual_asset');
    const firstLevelSubjects = assetCategory?.children;
    
    // 检查科目标题格式：代码 + 名称
    const cashSubject = firstLevelSubjects?.find((subject: any) => subject.key === '库存现金');
    expect(cashSubject?.title).toBe('1001 库存现金');
    
    const bankSubject = firstLevelSubjects?.find((subject: any) => subject.key === '银行存款');
    expect(bankSubject?.title).toBe('1002 银行存款');
  });

  it('应该正确处理叶子节点标识', () => {
    const treeData = buildCompleteSubjectTreeData(mockSubjectData);
    
    const assetCategory = treeData.find(node => node.key === 'actual_asset');
    const firstLevelSubjects = assetCategory?.children;
    
    // 库存现金是叶子节点
    const cashSubject = firstLevelSubjects?.find((subject: any) => subject.key === '库存现金');
    expect(cashSubject?.isLeaf).toBe(true);
    
    // 银行存款不是叶子节点
    const bankSubject = firstLevelSubjects?.find((subject: any) => subject.key === '银行存款');
    expect(bankSubject?.isLeaf).toBe(false);
  });
});
